# 🔮 Previsão e Análise de Preço do Bitcoin (BTC/USDT)

Este projeto realiza **análises técnicas, previsões de movimento de mercado** e envia **alertas automatizados para o Discord** com base nos dados do par BTC/USDT obtidos da Binance. Utiliza técnicas de Machine Learning, Deep Learning (PyTorch & LSTM) e indicadores técnicos populares.

---

## 🚀 Funcionalidades

- ✅ Coleta de dados históricos da Binance
- 📈 Cálculo de indicadores técnicos:
  - Mé<PERSON>s móveis (MA)
  - RSI
  - Bandas de Bollinger
  - MACD
  - ATR
  - OBV
- 🤖 Previsão com modelos:
  - Random Forest
  - Rede Neural Simples (PyTorch)
  - LSTM (Keras)
- 🔗 Envio de alertas automáticos para Discord com:
  - Direção prevista do mercado
  - Sinais técnicos (ex: RSI sobrevendido)
  - Simulação de trade com ganho/perda estimada
  - Gráfico com análise técnica
- 📊 Visualização gráfica com indicadores e projeções de preços

---

## 🧠 Modelos de Machine Learning

| Modelo        | Descrição                                  |
| ------------- | ------------------------------------------ |
| Random Forest | Modelo clássico para classificação binária |
| PyTorch NN    | Rede neural densa simples para previsão    |
| LSTM (Keras)  | Rede recorrente para aprendizado temporal  |

---

## ⚙️ Requisitos

```bash
pip install pandas numpy matplotlib seaborn sklearn torch keras requests joblib
```
