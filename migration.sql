CREATE TABLE teams (
    id SERIAL PRIMARY KEY,
    external_id INT UNIQUE,
    name <PERSON><PERSON><PERSON><PERSON>(100),
    short_name <PERSON><PERSON><PERSON><PERSON>(50)
);

CREATE TABLE leagues (
    id SERIAL PRIMARY KEY,
    external_id INT UNIQUE,
    name VA<PERSON>HA<PERSON>(100),
    season VARCHAR(20),
    country VARCHAR(50)
);

CREATE TABLE matches (
    id SERIAL PRIMARY KEY,
    external_id INT UNIQUE,
    league_id INT REFERENCES leagues(id),
    home_team_id INT REFERENCES teams(id),
    away_team_id INT REFERENCES teams(id),
    match_date TIMESTAMP,
    round VARCHAR(50),
    home_goals INT,
    away_goals INT,
    status VARCHAR(20)
);

CREATE TABLE notifications (
    id SERIAL PRIMARY KEY,
    match_id INT REFERENCES matches(id),
    notified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
