import requests
import json
import os
import time

API_KEY = 'de5775dc130ab10e258ecb3cca519669'
BASE_URL = 'https://v3.football.api-sports.io'

HEADERS = {
    'x-apisports-key': API_KEY
}

DISCORD_WEBHOOK_URL = 'https://discord.com/api/webhooks/1377396166383243315/LeIDwHUhBK6iL_DOg-zDPkt3ChXGqAjXw_M4I25zpEVUJtjRRHdFFslJhH9d_yrIpl1d'

def send_discord_message(webhook_url, message):
    data = {
        "content": message
    }
    response = requests.post(webhook_url, json=data)
    if response.status_code == 204:
        print("[OK] Mensagem enviada ao Discord com sucesso!")
    else:
        print(f"[ERRO] Falha ao enviar mensagem: {response.status_code} - {response.text}")

def get_team_id_by_name(name):
    print(f"[LOG] Buscando ID do time pelo nome: {name}")
    params = {'search': name}
    response = requests.get(f'{BASE_URL}/teams', headers=HEADERS, params=params)
    data = response.json()
    if data['response']:
        team_id = data['response'][0]['team']['id']
        print(f"[OK] Encontrado ID do time {name}: {team_id}")
        return team_id
    print(f"[WARN] Time não encontrado: {name}")
    return None

def get_league_id(name='Serie A', country='Brazil', season=2023):
    print(f"[LOG] Buscando ID da liga: {name} - {country} - {season}")
    params = {'name': name, 'country': country, 'season': season}
    response = requests.get(f'{BASE_URL}/leagues', headers=HEADERS, params=params)
    data = response.json()
    if data.get('response'):
        for item in data['response']:
            league = item.get('league', {})
            country_data = item.get('country', {})
            if league.get('name') == name and country_data.get('name') == country:
                league_id = league['id']
                print(f"[OK] Liga encontrada: ID = {league_id}")
                return league_id, season
    print(f"[ERRO] Liga não encontrada: {name} / {country} / {season}")
    print(json.dumps(data, indent=2))
    return None, None

def get_fixtures_by_league_and_round(league_id, season, round_name):
    print(f"[LOG] Buscando jogos da liga {league_id}, temporada {season}, rodada '{round_name}'")
    params = {
        'league': league_id,
        'season': season,
        'round': round_name
    }
    response = requests.get(f'{BASE_URL}/fixtures', headers=HEADERS, params=params)
    data = response.json()
    if 'response' not in data:
        print(f"[ERRO] Falha ao obter jogos: {data}")
        return []
    return data['response']

def get_last_matches(team_id, league_id, season, limit=5):
    params = {
        'team': team_id,
        'league': league_id,
        'season': season
    }
    response = requests.get(f'{BASE_URL}/fixtures', headers=HEADERS, params=params)
    data = response.json()
    jogos = data.get('response', [])
    # Pega os últimos 'limit' jogos manualmente, ordenando pela data
    jogos_ordenados = sorted(jogos, key=lambda x: x['fixture']['date'], reverse=True)
    return jogos_ordenados[:limit]

def calculate_prediction(last_matches_team1, last_matches_team2):
    goals_team1 = [m['goals']['for'] for m in last_matches_team1 if m.get('goals') and m['goals'].get('for') is not None]
    goals_team2 = [m['goals']['for'] for m in last_matches_team2 if m.get('goals') and m['goals'].get('for') is not None]

    if not goals_team1 or not goals_team2:
        print("[WARN] Dados insuficientes para cálculo de média de gols.")
        return {
            'team1_avg_goals': 0,
            'team2_avg_goals': 0,
            'prediction': 'Dados insuficientes para previsão'
        }

    avg_goals_team1 = sum(goals_team1) / len(goals_team1)
    avg_goals_team2 = sum(goals_team2) / len(goals_team2)

    if avg_goals_team1 > avg_goals_team2:
        prediction = 'Vitória do Time 1'
    elif avg_goals_team2 > avg_goals_team1:
        prediction = 'Vitória do Time 2'
    else:
        prediction = 'Empate provável'

    print(f"[LOG] Média gols Time 1: {avg_goals_team1:.2f}, Time 2: {avg_goals_team2:.2f}, Previsão: {prediction}")

    return {
        'team1_avg_goals': avg_goals_team1,
        'team2_avg_goals': avg_goals_team2,
        'prediction': prediction
    }

def send_to_discord(content):
    data = {
        'content': content
    }
    response = requests.post(DISCORD_WEBHOOK_URL, json=data)
    if response.status_code != 204:
        print(f'Erro ao enviar para o Discord: {response.status_code} - {response.text}')
    else:
        print('Mensagem enviada ao Discord com sucesso!')

def process_all_brasileirao_matches():
    league_id, season = get_league_id()
    if not league_id:
        print("[ERRO] Não foi possível obter ID da liga.")
        send_to_discord("⚠️ Erro: Não foi possível obter ID da liga para análise.")
        return

    round_to_process = 'Regular Season - 10'
    fixtures = get_fixtures_by_league_and_round(league_id, season, round_to_process)

    print(f"[LOG] Total de jogos encontrados na rodada {round_to_process}: {len(fixtures)}")

    mensagens = []  # Lista para acumular mensagens de análise

    for fixture in fixtures:
        fixture_id = fixture['fixture']['id']
        home_team = fixture['teams']['home']
        away_team = fixture['teams']['away']

        if not home_team['id'] or not away_team['id']:
            print(f"[WARN] Ignorando jogo {fixture_id} com time(s) indefinido(s).")
            continue

        
        last_home = get_last_matches(home_team['id'], league_id, season)
        last_away = get_last_matches(away_team['id'], league_id, season)

        if len(last_home) < 3 and len(last_away) < 3:
            print(f"[WARN] Ignorando jogo {fixture_id} por dados insuficientes (Home: {len(last_home)}, Away: {len(last_away)})")
            continue

        print("[DEBUG] Chamando calculate_prediction")
        resultado = calculate_prediction(last_home, last_away)
        print("[DEBUG] Resultado da predição:", resultado)

        mensagem = (
            f"📊 **Análise {home_team['name']} vs {away_team['name']}**\n"
            f"🏠 Média gols {home_team['name']}: {resultado['team1_avg_goals']:.2f}\n"
            f"🚗 Média gols {away_team['name']}: {resultado['team2_avg_goals']:.2f}\n"
            f"🔮 **Previsão:** {resultado['prediction']}\n"
            "-------------------------------------------"
        )

        mensagens.append(mensagem)

    # Monta o payload final
    if mensagens:
        payload = "\n".join(mensagens)
    else:
        payload = "ℹ️ Nenhuma análise foi gerada para a rodada selecionada."

    send_to_discord(payload)


if __name__ == "__main__":
    process_all_brasileirao_matches()